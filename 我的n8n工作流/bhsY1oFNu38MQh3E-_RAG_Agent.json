{"name": "自动向量化+RAG Agent", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-200, -60], "id": "16adaa15-aaa8-4384-bfb4-dd814283473a", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "fileFolder", "filter": {"folderId": {"__rl": true, "value": "1V-LgLTvCg96Fen1AIJ0m5McI40pEoFjS", "mode": "list", "cachedResultName": "RAGn8n", "cachedResultUrl": "https://drive.google.com/drive/folders/1V-LgLTvCg96Fen1AIJ0m5McI40pEoFjS"}}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [20, -60], "id": "7677cfcd-f828-4862-a82e-a10076a724b8", "name": "Search files and folders", "credentials": {"googleDriveOAuth2Api": {"id": "6mniYl7M1EmZAcC1", "name": "Google Drive account"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [240, -60], "id": "ab6521ad-7a2b-46bd-8bbc-3e3dab436921", "name": "Download file", "credentials": {"googleDriveOAuth2Api": {"id": "6mniYl7M1EmZAcC1", "name": "Google Drive account"}}}, {"parameters": {"mode": "insert", "pineconeIndex": {"__rl": true, "value": "ragn8n", "mode": "list", "cachedResultName": "ragn8n"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.3, "position": [460, -60], "id": "bb025004-ae2f-45f5-aa53-23fbcecde46d", "name": "Pinecone Vector Store", "credentials": {"pineconeApi": {"id": "0sXSTZ59v2JrcZKT", "name": "PineconeApi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [400, 160], "id": "42bae552-6219-4193-927e-678610d282bd", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "hWpukzj4RdaTPKx2", "name": "OpenAi account"}}}, {"parameters": {"dataType": "binary", "options": {"metadata": {"metadataValues": [{"name": "file-name", "value": "={{ $json.name }}"}]}}}, "type": "@n8n/n8n-nodes-langchain.documentDefaultDataLoader", "typeVersion": 1.1, "position": [580, 160], "id": "591b9286-e6df-4a1f-a34c-01906337f8cb", "name": "Default Data Loader"}, {"parameters": {"content": "实现在 google drive 文件夹中更新的文档可以自动导入pinecone 进行向量化"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-360, 120], "id": "8d475417-0cfb-4cb3-b0bc-88deec92af53", "name": "<PERSON><PERSON>"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyHour"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1V-LgLTvCg96Fen1AIJ0m5McI40pEoFjS", "mode": "list", "cachedResultName": "RAGn8n", "cachedResultUrl": "https://drive.google.com/drive/folders/1V-LgLTvCg96Fen1AIJ0m5McI40pEoFjS"}, "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [20, 120], "id": "55119ff3-b48a-47d3-a1c4-bc46fa902466", "name": "Google Drive Trigger", "credentials": {"googleDriveOAuth2Api": {"id": "6mniYl7M1EmZAcC1", "name": "Google Drive account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.chatTrigger", "typeVersion": 1.1, "position": [80, 560], "id": "e138ac64-4386-46de-a26a-37989604f278", "name": "When chat message received", "webhookId": "2b035869-6626-4e35-95f8-31ca57ac5d77"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 2, "position": [300, 560], "id": "521b3325-c400-46e0-a597-0583e7931feb", "name": "AI Agent"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4.1-nano", "mode": "list", "cachedResultName": "gpt-4.1-nano"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [180, 780], "id": "a2d516b6-c4a6-4f8d-a302-3cceb6cd0b32", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "hWpukzj4RdaTPKx2", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.memoryBufferWindow", "typeVersion": 1.3, "position": [340, 780], "id": "ff61a5c7-ef00-41d2-bd4f-c98dee3ad0b8", "name": "Simple Memory"}, {"parameters": {"description": "Youtube AI info"}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1.1, "position": [500, 780], "id": "96f85dbb-0e82-47b6-a690-d3c2c5dbbb2f", "name": "Answer questions with a vector store"}, {"parameters": {"model": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "gpt-4o-mini"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1.2, "position": [620, 1000], "id": "0988bed8-5aeb-4f5a-aa0c-17d30c08452b", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "hWpukzj4RdaTPKx2", "name": "OpenAi account"}}}, {"parameters": {"pineconeIndex": {"__rl": true, "value": "ragn8n", "mode": "list", "cachedResultName": "ragn8n"}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "typeVersion": 1.3, "position": [340, 1000], "id": "dd690a85-0662-4cb0-8ad0-2f37102848d2", "name": "Pinecone Vector Store1", "credentials": {"pineconeApi": {"id": "0sXSTZ59v2JrcZKT", "name": "PineconeApi account"}}}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.2, "position": [340, 1160], "id": "53982f29-ac6f-45dc-999d-994f864dd8e1", "name": "Embeddings OpenAI1", "credentials": {"openAiApi": {"id": "hWpukzj4RdaTPKx2", "name": "OpenAi account"}}}, {"parameters": {"content": "调用 pinecone 数据库进行对话"}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-360, 560], "id": "4793cfb2-3417-4c02-a990-9390e5cf6ba1", "name": "Sticky Note1"}, {"parameters": {"content": "", "height": 400, "width": 1240, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-380, -100], "typeVersion": 1, "id": "9c4b9520-5c69-401e-a15e-dfbcd289529a", "name": "Sticky Note2"}, {"parameters": {"content": "", "height": 780, "width": 1240, "color": 4}, "type": "n8n-nodes-base.stickyNote", "position": [-380, 540], "typeVersion": 1, "id": "92f1e367-5e8c-4009-a4e9-bc7a010c58c5", "name": "Sticky Note3"}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Search files and folders", "type": "main", "index": 0}]]}, "Search files and folders": {"main": [[{"node": "Download file", "type": "main", "index": 0}]]}, "Download file": {"main": [[{"node": "Pinecone Vector Store", "type": "main", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "Pinecone Vector Store", "type": "ai_embedding", "index": 0}]]}, "Default Data Loader": {"ai_document": [[{"node": "Pinecone Vector Store", "type": "ai_document", "index": 0}]]}, "Google Drive Trigger": {"main": [[{"node": "Download file", "type": "main", "index": 0}]]}, "When chat message received": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}, "OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Simple Memory": {"ai_memory": [[{"node": "AI Agent", "type": "ai_memory", "index": 0}]]}, "Answer questions with a vector store": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Answer questions with a vector store", "type": "ai_languageModel", "index": 0}]]}, "Pinecone Vector Store1": {"ai_vectorStore": [[{"node": "Answer questions with a vector store", "type": "ai_vectorStore", "index": 0}]]}, "Embeddings OpenAI1": {"ai_embedding": [[{"node": "Pinecone Vector Store1", "type": "ai_embedding", "index": 0}]]}}, "settings": {"executionOrder": "v1"}, "staticData": null, "pinData": {}, "triggerCount": 0, "meta": {"templateCredsSetupCompleted": true}}