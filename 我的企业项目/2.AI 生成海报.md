项目二：AI 生成海报 
## **背景与目标**

希望解决海报封面设计的效率问题，通过 AI 生成工具实现“一键生成”的自动化流程。

## **需求详情**

### 1. 新增 AI 生成海报分享

- 会员权益新增 **AI 生成海报，**非会员点击生成海报按钮显示升级弹窗提示升级。

![image.png](https://tc-cdn.flowus.cn/oss/32e0ef00-7d20-437b-9205-1ed16b30cb06/image.png?time=1753253100&token=d5070029b99b611784399345ad510d9d5420e882ca81f0ddfe78d17ed6d9745a&role=free)

- 分享页面可使用；

![image.png](https://tc-cdn.flowus.cn/oss/36d057ec-0066-4818-aaf0-6fa2d45d496f/image.png?time=1753253100&token=8de2a9643306e8b71a89acc2d588d9ee647152488f4ea7ea09954b57fd7627fd&role=free)

- 分享 AI 搜索回答可使用；

  - 注意：AI 搜索回答不支持显示二维码和空间信息，隐藏掉开关

![image.png](https://tc-cdn.flowus.cn/oss/eb10fc37-6739-433d-83b2-d6437d496f58/image.png?time=1753253100&token=994d89c94f7bc444811c3ffd80256c6dbff785b0f182f26fdb5410701b2236b7&role=free)

### 2. AI 生成海报设置

AI 生成海报**设置**如下： （页面生成海报版）

1. 提供两种**海报尺寸**：

  - 3:4（宽:高）适配小红书封面

  - 自由比例（宽度为440px，高度不超过1280px）

2. 点击按钮【AI 生成海报】调用 DeepSeek V3 生成 html 代码，生成过程显示动效，滚动显示文案：内容处理中...；海报生成中...

![image.png](https://tc-cdn.flowus.cn/oss/4a9684b7-c855-4226-b13b-7f9b88f22618/image.png?time=1753253100&token=8b1037131c3f6452fa36adca159910c21835d294ee3db6eabe59a878efd1954b&role=free)

3. 支持开关控制页面**二维码**显示/不显示，默认打开（显示）

4. 支持开关控制**空间信息**（头像+昵称）显示/不显示，默认打开（显示）

![image.png](https://tc-cdn.flowus.cn/oss/5b2573c8-9ff5-4271-bc88-0a909773ad43/image.png?time=1753253100&token=d2facb60e1546e36062392372bdc8b567ed5dee89904da5fc8a80fbbb798d2b6&role=free)

### 3. 动态数据填充

- 需要嵌入产品 logo 图片链接

- 需要动态替换提示词中用户的页面二维码图片链接、空间头像、空间名称、文档内容

![image.png](https://tc-cdn.flowus.cn/oss/e88ae2f5-046e-4dc9-ae9b-aab5ec7d47c7/image.png?time=1753253100&token=6422251f9a02a3923d155b67f28ed98ec7eeb59b81430864d09d8f5531c4669c&role=free)

![image.png](https://tc-cdn.flowus.cn/oss/a06a231b-9c1c-47ea-b32e-07ad285fcee5/image.png?time=1753253100&token=bf94f079d1511c9c85167758673da5a85010406e9b24d40d4e26c422f082e49b&role=free)

### 4. 不保存历史生成记录

- 当前版本不保存历史生成记录，重新点击生成海报将覆盖原本的海报；

- 显示提示文案告知重新生成海报前先保存；

  ![image.png](https://tc-cdn.flowus.cn/oss/3f1b035f-4c7a-4aa5-b0d0-8edc7085be59/image.png?time=1753253100&token=98b2482b6bf97dab03cd140e14ea670e7d0054c62c2390f1a255d9f96240c197&role=free)



### 5. 生成海报提示词

### 比例 3:4

#### 1. 二维码✅

显示二维码 ✅；显示空间信息 ☑️

```Markdown
、请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 核心要点列表：以符合风格的方式呈现列表内容
* 二维码区域：将二维码融入整体设计
* 产品logo区域：将logo融入整体设计
* **布局约束**：所有设计元素（包括文字、图像、装饰）必须严格控制在 440px 宽度和 **587px** 高度内。确保元素间有适当的间距和留白，避免内容过于拥挤或贴近边缘。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **固定比例 3:4 **（宽度固定为 **440px**，高度固定为 **587px**），**任何元素绝对不能超出此范围，包括元素的边框、阴影或任何视觉效果**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为30-40字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 二维码截图地址：（必须显示）：{二维码图片链接}
- 二维码尺寸：80px 宽度，80px 高度
- 二维码旁边显示文案：微信扫码查看
- 产品logo：（必须显示,提供两种样式需要根据风格选择）：(黑底)https://cdn2.flowus.cn/icon/fu_icon_black.svg, (白底)https://cdn2.flowus.cn/icon/fu_icon_white.svg
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (固定尺寸 440px宽587px高):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、587px高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（二维码、产品logo）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

返回完整html代码，不返回文字说明
```


  

#### 2. 空间信息✅

显示二维码 ☑️；显示空间信息 ✅

```Markdown
请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 核心要点列表：以符合风格的方式呈现列表内容
* 产品logo：（必须显示,提供两种样式需要根据风格选择）：(黑底)https://cdn2.flowus.cn/icon/fu_icon_black.svg, (白底)https://cdn2.flowus.cn/icon/fu_icon_white.svg
* **布局约束**：所有设计元素（包括文字、图像、装饰）必须严格控制在 440px 宽度和 **587px** 高度内。确保元素间有适当的间距和留白，避免内容过于拥挤或贴近边缘。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **固定比例 3:4 **（宽度固定为 **440px**，高度固定为 **587px**），**任何元素绝对不能超出此范围，包括元素的边框、阴影或任何视觉效果**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为30-40字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 空间名称：（必须显示）：{空间名称}   
- 空间头像：（必须显示）：{头像链接}
- 产品logo：（必须显示）：{logo 链接}
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准 (基于 4:3 )
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (固定尺寸 440px宽587px高):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、587px高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（头像、空间名称、产品logo）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

只返回html代码，不返回文字说明
```




#### 3. 都不显示

显示二维码 ☑️；显示空间信息 ☑️；

```Markdown
请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 核心要点列表：以符合风格的方式呈现列表内容
* 产品 logo 区域：将产品 logo 融入整体设计
* **布局约束**：所有设计元素（包括文字、图像、装饰）必须严格控制在 440px 宽度和 **587px** 高度内。确保元素间有适当的间距和留白，避免内容过于拥挤或贴近边缘。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **固定比例 3:4 **（宽度固定为 **440px**，高度固定为 **587px**），**任何元素绝对不能超出此范围，包括元素的边框、阴影或任何视觉效果**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为30-40字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 产品logo：（必须显示）：{logo 链接}
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (固定尺寸 440px宽587px高):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、587px高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（产品logo）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

只返回html代码，不返回文字说明
```




#### 4. 二维码+空间信息✅

显示二维码 ✅；显示空间信息 ✅

```Markdown
请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 核心要点列表：以符合风格的方式呈现列表内容
* 二维码区域：将二维码融入整体设计
* 产品 logo 区域：将产品 logo 融入整体设计
* **布局约束**：所有设计元素（包括文字、图像、装饰）必须严格控制在 440px 宽度和 **587px** 高度内。确保元素间有适当的间距和留白，避免内容过于拥挤或贴近边缘。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **固定比例 3:4 **（宽度固定为 **440px**，高度固定为 **587px**），**任何元素绝对不能超出此范围，包括元素的边框、阴影或任何视觉效果**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为30-40字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 二维码截图地址：（必须显示）：{二维码图片链接}
- 二维码尺寸：80px 宽度，80px 高度
- 二维码旁边显示文案：微信扫码查看
- 空间名称：（必须显示）：{空间名称}   
- 空间头像：（必须显示）：{头像链接}
- 产品logo：（必须显示）：{logo 链接}
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (固定尺寸 440px宽587px高):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、587px高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（二维码、头像、空间名称、产品 logo等）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

只返回html代码，不返回文字说明
```




### 自由比例

#### 1. 二维码✅

显示二维码 ✅；显示空间信息 ☑️

```Markdown
请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 核心要点列表：以符合风格的方式呈现列表内容
* 二维码区域：将二维码融入整体设计
* 产品 logo 区域：将 logo 融入整体设计
* **画布与布局约束**: 整个设计必须构成一个**完全独立的视觉单元**，并被严格限制在**一个无边框 (`border: 0;`) 的主容器 `div`（画布）内**。此画布的尺寸必须是 **宽度 440px，最大高度 1280px**。**所有**视觉元素（包括文字、图像、装饰、背景、阴影、效果）**绝对不能超出**此画布`div`的边界。推荐在画布 `div` 上明确设置 `overflow: hidden;` 来强制约束内容。此画布 `div` 将是**最终用于外部截图或程序化下载的完整区域**。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **输出为独立画布**: 最终生成的 HTML/CSS 代码应**仅包含海报本身的视觉呈现**，完全封装在上述 **440px x (≤1280px) 的无边框画布 `div`** 中。确保**任何视觉元素（包括边框、阴影、外发光等效果）都计算在此尺寸内，绝不溢出画布边界**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为50-60字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 二维码截图地址：（必须显示）：{二维码图片链接}
- 二维码尺寸：80px 宽度，80px 高度
- 二维码旁边显示文案：微信扫码查看
- 产品logo：（必须显示）：{logo 链接}
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (自由比例宽度440px，高度不超过1280px):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、最大 1280px 高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（二维码、产品logo等）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

只返回html代码，不返回文字说明
```




#### 2. 空间信息✅

显示二维码 ☑️；显示空间信息 ✅

```Markdown
请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 核心要点列表：以符合风格的方式呈现列表内容
* 产品 logo 区域：将 logo 融入整体设计
* **画布与布局约束**: 整个设计必须构成一个**完全独立的视觉单元**，并被严格限制在**一个无边框 (`border: 0;`) 的主容器 `div`（画布）内**。此画布的尺寸必须是 **宽度 440px，最大高度 1280px**。**所有**视觉元素（包括文字、图像、装饰、背景、阴影、效果）**绝对不能超出**此画布`div`的边界。推荐在画布 `div` 上明确设置 `overflow: hidden;` 来强制约束内容。此画布 `div` 将是**最终用于外部截图或程序化下载的完整区域**。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **输出为独立画布**: 最终生成的 HTML/CSS 代码应**仅包含海报本身的视觉呈现**，完全封装在上述 **440px x (≤1280px) 的无边框画布 `div`** 中。确保**任何视觉元素（包括边框、阴影、外发光等效果）都计算在此尺寸内，绝不溢出画布边界**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为50-60字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 空间名称：（必须显示）：{空间名称}   
- 空间头像：（必须显示）：{头像链接}
- 产品logo：（必须显示）：{logo 链接}
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准 (基于自由比例)
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (自由比例宽度440px，高度不超过1280px):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、最大 1280px 高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（头像、空间名称、产品logo等）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

只返回html代码，不返回文字说明
```




#### 3. 都不显示

显示二维码 ☑️；显示空间信息 ☑️

```Markdown
请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 产品 logo 区域：将 logo 融入整体设计
* 核心要点列表：以符合风格的方式呈现列表内容
* **画布与布局约束**: 整个设计必须构成一个**完全独立的视觉单元**，并被严格限制在**一个无边框 (`border: 0;`) 的主容器 `div`（画布）内**。此画布的尺寸必须是 **宽度 440px，最大高度 1280px**。**所有**视觉元素（包括文字、图像、装饰、背景、阴影、效果）**绝对不能超出**此画布`div`的边界。推荐在画布 `div` 上明确设置 `overflow: hidden;` 来强制约束内容。此画布 `div` 将是**最终用于外部截图或程序化下载的完整区域**。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **输出为独立画布**: 最终生成的 HTML/CSS 代码应**仅包含海报本身的视觉呈现**，完全封装在上述 **440px x (≤1280px) 的无边框画布 `div`** 中。确保**任何视觉元素（包括边框、阴影、外发光等效果）都计算在此尺寸内，绝不溢出画布边界**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为50-60字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 产品logo：（必须显示）：{logo 链接}
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (自由比例宽度440px，高度不超过1280px):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、最大 1280px 高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（产品logo）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

只返回html代码，不返回文字说明
```




#### 4. 二维码+空间信息✅

显示二维码 ✅；显示空间信息 ✅

```Markdown
请从以下29种设计风格中随机选择1种，设计高级时尚杂志风格的知识卡片，将日常信息以精致奢华的杂志编排呈现，让用户感受到如同翻阅高端杂志般的视觉享受。

**可选设计风格：**

1. 极简主义风格 (Minimalist)
采用极简主义风格设计，遵循"少即是多"的理念。使用大量留白创造呼吸空间，仅保留最必要的元素。配色方案限制在2-3种中性色，主要为白色背景配以黑色或深灰色文字。排版应精确到像素级别，使用精心设计的网格系统和黄金比例。字体选择无衬线字体如Helvetica或Noto Sans，字重变化作为主要层次手段。装饰元素几乎为零，仅使用极细的分隔线和微妙的阴影。整体设计应呈现出克制、优雅且永恒的美学，让内容本身成为焦点。参考Dieter Rams的设计原则和日本无印良品(MUJI)的产品美学。

2. 大胆现代风格 (Bold Modern)
采用大胆现代风格设计，打破传统排版规则，创造强烈视觉冲击。使用鲜艳对比色如荧光粉、电子蓝、亮黄等，背景可使用深色或鲜艳色块。排版应不对称且动态，标题文字极大（至少60px），可使用极粗字重或压缩字体，甚至允许文字重叠和溢出。图形元素应用几何形状，边缘锐利，可添加不规则裁切效果。层次感通过大小、颜色和位置的极端对比创造。整体设计应充满张力和活力，像一张视觉宣言，参考Wired杂志和Pentagram设计工作室的作品。添加微妙动效如悬停放大或颜色变换，增强现代感。

3. 优雅复古风格 (Elegant Vintage)
采用优雅复古风格设计，重现20世纪初期印刷品的精致美学。使用米色或淡黄色纸张质感背景，配以深棕、暗红等老式印刷色。字体必须使用衬线字体如Baskerville或Noto Serif，标题可使用装饰性字体。排版应对称且庄重，遵循传统书籍设计原则。装饰元素包括精致的花纹边框、古典分隔线和角落装饰，可添加轻微做旧效果如纸张纹理和微妙污点。图像应用复古滤镜处理，呈现褪色照片效果。整体设计应散发出典雅、成熟且历经时间考验的气质，参考The New Yorker和老式法国时尚杂志的设计语言。

4. 未来科技风格 (Futuristic Tech)
采用未来科技风格设计，呈现高度发达的数字界面美学。背景必须使用深蓝或纯黑，配以霓虹蓝、电子紫等高饱和度荧光色。排版应模拟高科技显示界面，使用等宽字体如Space Mono，添加数据可视化元素如图表、网格和代码片段。装饰元素包括科技感线条、HUD界面框架和全息投影效果。必须添加动态元素如扫描线、数据流动效果和微妙闪烁。可使用半透明叠加层和模糊效果创造深度。整体设计应呈现出未来感、高科技和信息密集的视觉体验，仿佛来自几十年后的界面，参考《银翼杀手2049》和《攻壳机动队》的视觉设计。

5. 斯堪的纳维亚风格 (Scandinavian)
采用斯堪的纳维亚风格设计，体现北欧设计的简约与功能美学。使用纯白背景，配以特定的北欧色调如淡蓝、浅灰、原木色和淡粉。排版应极度克制且有序，使用大量留白，但与极简主义不同，应加入温暖质感。字体选择无衬线几何字体如Futura或Circular，字重轻盈。装饰元素应极少但精心选择，可使用简单几何图案如三角形和线条，参考马勒维奇的构成主义。图像应明亮、简洁且自然。整体设计应呈现出清爽、实用且温暖的北欧特质，平衡美学与功能性，参考Kinfolk杂志和丹麦设计品牌HAY的产品美学。

6. 艺术装饰风格 (Art Deco)
采用艺术装饰风格设计，重现1920-30年代的奢华与几何美学。必须使用黑金配色方案，金色应为真实金属色#D4AF37而非黄色。排版应严格对称，使用装饰性强的字体，特别是几何感强烈的字体如Broadway或现代变体。装饰元素是关键，必须包含扇形放射纹、锯齿形、几何图案和对称花纹。边框应华丽且具结构性，角落处理需特别精致。可添加仿金箔和大理石纹理增强奢华感。整体设计应呈现出大胆、奢华且充满时代特色的视觉效果，仿佛来自爵士时代的纽约或巴黎，参考克莱斯勒大厦和《了不起的盖茨比》电影海报的视觉语言。

7. 日式极简风格 (Japanese Minimalism)
采用日式极简风格设计，体现"侘寂"(Wabi-Sabi)美学——接受不完美、无常与不完整的哲学。使用极度克制的色彩，主要为白、灰、黑和淡墨色。留白(Ma)是核心元素，至少70%的设计应为空白，创造宁静感。排版应非对称且垂直，可使用垂直书写模式，体现日本传统排版。字体应极度简约，笔画轻盈。装饰元素几乎为零，但可添加一处墨迹、简单印章或单一线条作为点睛之笔。整体设计应呈现出深度宁静、精致且富有禅意的视觉体验，仿佛一页来自京都寺院的书页，参考原研哉的MUJI设计理念和日本传统水墨画的留白美学。

8. 后现代解构风格 (Postmodern Deconstruction)
采用后现代解构风格设计，彻底打破传统设计规则和网格系统。排版应故意不规则且混乱，使用多种字体、大小和方向，文字可重叠、倾斜或被切割。必须使用不和谐的色彩组合，打破传统配色规则。图形元素应包含随机几何形状、不完整图形和故意扭曲的元素。层次感通过混乱中的秩序创造，可使用碎片化图像和拼贴效果。装饰元素应看似随意但精心安排，如手绘线条、涂鸦和复印机错误效果。整体设计应挑战视觉常规，创造一种有控制的混乱美学，参考David Carson的Ray Gun杂志设计和Wolfgang Weingart的实验性排版作品。

9. 朋克风格 (Punk)
采用朋克风格设计，体现DIY精神和反叛文化。必须使用粗糙、手工制作的视觉效果，如剪贴报纸、复印机扭曲和胶带痕迹。色彩应高对比且原始，主要使用黑、白、红色，可添加荧光色点缀。排版必须粗暴且不规则，使用手写、喷漆或剪贴字体，文字可被切割或部分遮挡。装饰元素应包含安全别针、胶带、污渍和撕裂效果。图像应使用高对比度、粗颗粒处理，模拟劣质印刷。必须添加随机元素如涂鸦、X标记和感叹号。整体设计应呈现出原始、粗糙且充满能量的视觉冲击，仿佛一张来自70-80年代伦敦或纽约地下场景的传单，参考Sex Pistols的专辑封面和早期朋克杂志。

10. 英伦摇滚风格 (British Rock)
采用英伦摇滚风格设计，融合英国传统元素与反叛摇滚美学。色彩应使用英国国旗色系（红、白、蓝）或复古棕色调，可添加做旧效果。排版应混合经典与现代，使用衬线字体与手写字体的组合，标题可使用哥特式或维多利亚风格字体。装饰元素应包含英国符号的现代演绎，如Union Jack图案、皇家纹章或伦敦地标的抽象表现。图像应使用复古滤镜，模拟老式胶片效果。可添加唱片、吉他或音符等音乐元素作为点缀。整体设计应呈现出典雅中带有叛逆、传统中融入现代的独特英伦风格，参考Oasis、The Beatles专辑封面和NME杂志的视觉语言。

11. 黑金属风格 (Black Metal)
采用黑金属风格设计，体现极致黑暗美学和神秘主义。背景必须为纯黑或极深灰度，创造压抑氛围。排版应使用古老、难以辨认的哥特式字体或锋利的几何字体，文字可扭曲或被符号干扰。装饰元素必须包含神秘符号、倒五角星、古代符文和神秘学图案。图像应高度对比且单色，可添加噪点和划痕增强原始感。边框应使用中世纪风格或神秘学几何图案。可添加微妙的闪烁效果模拟烛光。整体设计应呈现出神秘、冷酷且具仪式感的视觉体验，仿佛一本古老的神秘学典籍或挪威黑金属乐队的专辑封面，参考Darkthrone和Mayhem的视觉风格以及中世纪魔法书的排版。

12. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现80年代意大利设计运动的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。图像可使用几何框架或被几何形状切割。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考Ettore Sottsass的作品和《拯救大兵瑞恩》片头的视觉风格。

13. 赛博朋克风格 (Cyberpunk)
采用赛博朋克风格设计，体现"高科技，低生活"的反乌托邦美学。背景必须为深色（黑色或深蓝），配以霓虹色彩如荧光粉、电子蓝和酸性绿，创造夜间都市氛围。排版应模拟故障效果，使用像素化字体或未来感等宽字体，添加字符错位、扫描线和数字噪点。装饰元素必须包含科技界面、数据流、电路板图案和全息投影效果。必须添加故障艺术(Glitch Art)效果如RGB分离、数据损坏和画面撕裂。图像应高对比且添加霓虹光效，模拟雨夜霓虹灯反射。整体设计应呈现出未来主义与复古科技的混搭，营造一种数字化衰败的氛围，参考《银翼杀手》、《神经漫游者》和《赛博朋克2077》的视觉语言。

14. 波普艺术风格 (Pop Art)
采用波普艺术风格设计，重现60年代艺术运动的大胆美学。必须使用亮丽原色（红、黄、蓝）和黑色轮廓线，色彩应平面且不含渐变。排版应大胆且戏剧化，使用漫画风格字体和夸张的感叹词，文字可放置在对话气泡中。图像处理是关键，应使用半调网点(Halftone)效果模拟印刷，将图像简化为点阵和色块。装饰元素应包含漫画风格的线条、爆炸形状和拟声词。可使用重复图案和网格排列增强视觉冲击。整体设计应呈现出鲜明、直接且充满流行文化引用的视觉效果，仿佛一页来自60年代漫画或安迪·沃霍尔的艺术作品，参考Roy Lichtenstein的绘画和经典漫画书的视觉语言。

15. 瑞士国际主义风格的解构版 (Deconstructed Swiss Style)
采用瑞士国际主义风格的解构版设计，在严格网格系统的基础上进行有意识的破坏和重组。排版应基于经典瑞士网格，但故意打破和扭曲，文字可越过栏格、重叠或被切割。色彩应保持瑞士风格的克制，主要使用黑白，但可添加一两种鲜艳原色作为点缀。字体必须使用无衬线几何字体如Helvetica或Akzidenz-Grotesk，但可通过极端的字重、间距和大小变化创造张力。图像应高对比且可被解构或重组。装饰元素几乎为零，但可使用解构的网格线和参考点。整体设计应呈现出理性与叛逆并存的视觉效果，像是经典瑞士设计被有意识地挑战和重新诠释，参考Wolfgang Weingart和April Greiman的实验性作品。

16. 蒸汽波美学 (Vaporwave Aesthetics)
采用蒸汽波美学设计，体现互联网亚文化的怀旧未来主义。色彩必须使用特定的渐变组合，主要为粉紫色到青蓝色，创造黄昏或霓虹效果。排版应混合英文和日文/中文字符，使用80-90年代风格的衬线字体或早期数字字体，可添加阴影和辉光效果。装饰元素是关键，必须包含以下至少三种：罗马柱、棕榈树、格子地面、早期3D渲染、古典雕塑、日落、老式电脑界面。背景应使用网格线或星空。图像处理应添加VHS效果、扫描线和轻微失真。整体设计应呈现出一种超现实、梦幻且怀旧的数字美学，仿佛来自平行宇宙的90年代互联网，参考Floral Shoppe专辑封面和Windows 95界面元素的后现代重组。

17. 新表现主义风格 (Neo-Expressionism)
采用新表现主义风格设计，体现80年代艺术运动的原始能量和情感表达。色彩应强烈且不协调，使用原始、未经调和的色彩组合，可包含泼溅和涂抹效果。排版应富有表现力且不规则，使用手写或笔刷字体，文字可被部分遮挡或融入背景。线条必须粗犷且富有动感，展现明显的笔触和手工感。图像应被重新诠释，可添加涂鸦、划痕或重绘效果。装饰元素应看似随意但富有象征性，如原始符号、面具图案或抽象人物。整体设计应呈现出强烈的情感张力和原始能量，仿佛一幅由情感驱动的表现主义画作，参考Jean-Michel Basquiat和Georg Baselitz的作品风格。

18. 极简主义的极端版本 (Extreme Minimalism)
采用极简主义的极端版本设计，将"少即是多"的理念推向极致。留白必须占据至少90%的设计空间，创造极度的空旷感。色彩应限制在黑、白、灰三色，可添加一种极其微妙的强调色。排版应极度精简，每个元素的位置必须精确到像素级别，使用极细字重的无衬线字体，字号可极小但必须保持可读性。装饰元素完全消除，仅保留极细的分隔线或几何点。图像如必须使用，应被简化为最基本的线条或轮廓。整体设计应呈现出一种近乎禅意的纯粹和克制，让每个元素都具有绝对的必要性和目的性，参考John Pawson的建筑设计和Kenya Hara的平面设计作品。

19. 新未来主义 (Neo-Futurism)
采用新未来主义风格设计，体现当代建筑和产品设计中的前沿美学。形态应强调流线型曲线和有机几何形状，避免直角和静态形式。色彩应使用金属色调如银色、钛白和铬黄，配以一两种高饱和度的强调色。材质表现是关键，应模拟高科技材料如拉丝金属、碳纤维和磨砂玻璃。排版应动态且流畅，使用现代无衬线字体，可沿曲线排列或呈放射状。装饰元素应包含参数化生成的图案、流体动力学形态和仿生学结构。整体设计应呈现出高度发达的技术美学和动态感，仿佛来自近未来的高端产品，参考扎哈·哈迪德的建筑、特斯拉Cybertruck和Apple产品的设计语言。

20. 超现实主义数字拼贴 (Surrealist Digital Collage)
采用超现实主义数字拼贴风格设计，创造梦境般的视觉叙事。图像处理是核心，应组合不相关元素创造意外联系，如古典雕塑与现代电子产品、自然元素与几何形状。比例应故意失调，创造视觉张力。色彩可使用梦幻般的组合，如暖日落色调或冷月光色调，添加轻微的色偏。排版应融入拼贴中，文字可环绕物体、穿过图像或成为构图的一部分。装饰元素应包含超现实符号如悬浮物体、不可能的建筑、变形的人物或动物。可添加微妙的阴影和光效增强立体感。整体设计应呈现出一种介于现实与梦境之间的视觉体验，引发观者的想象和潜意识联想，参考René Magritte的绘画和现代数字艺术家如Justin Peters的作品。

21. 新巴洛克数字风格 (Neo-Baroque Digital)
采用新巴洛克数字风格设计，将17世纪的华丽美学重新诠释为数字形式。装饰是核心元素，应使用极其丰富的数字化巴洛克花纹、卷轴和浮雕效果，每个角落都应有精致细节。色彩应奢华且戏剧性，主要使用金色、深红、皇家蓝和黑色，可添加金属光泽和渐变效果。排版应华丽且层次丰富，使用装饰性强的衬线字体，可添加花体字母和装饰性首字母。图像应添加华丽框架和装饰性边缘。光影效果是关键，应创造强烈的明暗对比，模拟巴洛克绘画的戏剧性光效。整体设计应呈现出极度奢华、复杂且充满戏剧性的视觉体验，仿佛数字时代的凡尔赛宫，参考巴洛克艺术大师如鲁本斯的作品和现代奢侈品牌的视觉语言。

22. 液态数字形态主义 (Liquid Digital Morphism)
采用液态数字形态主义风格设计，结合流体动力学与数字艺术创造超前卫视觉体验。背景必须使用高级流体渐变，如紫罗兰到深蓝的流动过渡，并添加半透明的液态气泡或波浪形态。排版应具有流动感，文字可沿着液体路径排列或被液态效果部分包裹。装饰元素应模拟液体物理特性，如水滴、波纹或流体飞溅。色彩应使用梦幻般的液态渐变，如霓虹紫到电子蓝。必须添加微妙的动态效果，元素间的转换如同液体融合，文字可有轻微的波动或流动效果。图像应添加液态边框或流体遮罩。整体设计应呈现出一种超现实且高度未来感的流动视觉体验，仿佛界面本身是液态的，参考Björk的数字专辑视觉和Apple最新的流体动画设计语言。

23. 超感官极简主义 (Hypersensory Minimalism)
采用超感官极简主义风格设计，将极简美学推向感官极限。表面上看似极简，但通过微妙的纹理、触觉暗示和动态响应创造深层次感官体验。背景必须使用纯白或极浅灰，但添加几乎不可见的纹理变化，只有在光线变化或视角移动时才能察觉。排版应精确到像素级别，使用极细字重的无衬线字体，文字间距和行高必须遵循严格的数学比例。色彩应使用近似色调的细微变化，如不同程度的灰白或极淡的单色调，创造需要仔细观察才能发现的层次感。装饰元素应极少且极其微妙，如几乎不可见的线条或点。必须添加微妙的交互响应，如悬停时的轻微透明度变化或极其缓慢的颜色过渡。整体设计应呈现出一种"安静但深刻"的视觉体验，参考日本建筑师安藤忠雄的作品和苹果设计团队Jonathan Ive的产品美学。

24. 新表现主义数据可视化 (Neo-Expressionist Data Visualization)
采用新表现主义数据可视化风格设计，将抽象表现主义艺术与数据可视化完美融合。必须使用看似随意的笔触、泼溅和涂抹效果，但实际上是由精确数据驱动生成的可视化图表。背景应使用白色或浅色，但添加微妙的纹理和抽象笔触。排版应融入数据可视化中，文字可成为数据表达的一部分，使用不同字重和大小表示数值变化。色彩应使用鲜明且情感化的组合，如蓝色、红色、黄色等原色，但每种颜色都应对应特定数据类别。图表元素如条形、线条或点应具有手绘质感，展现明显的笔触和不规则边缘。整体设计应在混沌中呈现秩序，在抽象中传达精确信息，参考Giorgia Lupi的"数据人文主义"作品和Bloomberg Businessweek的实验性数据页面。

25. 维多利亚风格 (Victorian Style)
采用维多利亚风格设计，重现19世纪英国维多利亚时期的华丽印刷美学。背景必须使用米色或淡黄色纸张质感，配以棕色、深红和金色等传统印刷色调。边框是核心元素，应使用繁复的装饰花纹和卷草图案环绕整个设计，角落处需添加精致的装饰性图案。排版应严格对称且庄重，标题使用华丽的衬线字体或哥特式字体，并添加装饰性首字母。必须使用传统的分隔线、花饰和维多利亚时期的装饰符号。图像应添加精致的装饰性框架，可使用椭圆形或方形边框配以繁复图案。色彩应模拟老式印刷效果，添加微妙的做旧纹理和褪色效果。文字排版应遵循传统书籍设计原则，段落首行缩进，引用文字使用斜体并添加装饰性引号。整体设计应呈现出典雅、华丽且富有历史感的视觉效果，仿佛一页来自19世纪精装书籍或杂志的印刷品，参考William Morris的装饰图案设计和《潘趣》(Punch)杂志的版面设计。

26. 包豪斯风格 (Bauhaus)
采用包豪斯风格设计，体现20世纪早期德国包豪斯学校的功能主义美学。必须使用基本几何形状作为核心设计元素，如方形、圆形和三角形，保持形状的纯粹性。色彩应限制在基本原色——红、黄、蓝，配以黑白灰，不使用过渡色或渐变。排版应清晰且理性，使用无衬线字体如Futura或Helvetica，文字排列需遵循严格的网格系统，强调水平和垂直线条。标题应大胆且直接，可使用全大写字母增强视觉冲击力。装饰元素应完全服务于功能，避免纯粹装饰性的图案。图像处理应简洁且具有几何感，可使用高对比度的摄影或简化的图形。必须体现"形式服从功能"的设计理念，每个元素都应有明确目的。整体设计应呈现出理性、前卫且具有工业美感的视觉效果，仿佛一页来自1920年代包豪斯学校的教材或海报，参考拉斯洛·莫霍利-纳吉(László Moholy-Nagy)的排版设计和赫伯特·拜耶(Herbert Bayer)的海报作品。

27. 构成主义风格 (Constructivism)
采用构成主义风格设计，体现20世纪早期俄国前卫艺术运动的革命性美学。必须使用大胆的几何形状和对角线元素创造动态张力，强调结构与构成。色彩应限制在红、黑两色为主，可辅以少量白色或灰色，体现革命色彩。排版是关键元素，文字应成为设计的一部分而非简单的内容载体，可使用不同大小、粗细和方向的文字创造视觉层次，标题应大胆且具冲击力，可斜向排列或分割成多行。必须使用几何形状如三角形、圆形、直线和对角线作为主要视觉元素，这些元素应相互重叠和交织。照片或图像应使用锐利的对比度和几何化处理，可使用照片蒙太奇技术。整体构图应不对称且充满张力，仿佛元素间存在力的相互作用。可添加工业元素如齿轮、工厂或机械部件的抽象表现。整体设计应呈现出前卫、动态且具有政治宣传性质的视觉效果，参考亚历山大·罗德琴科(Alexander Rodchenko)和埃尔·利西茨基(El Lissitzky)的海报设计，体现"艺术进入生活"的设计理念。

28. 孟菲斯风格 (Memphis Design)
采用孟菲斯风格设计，重现1980年代意大利孟菲斯设计小组的前卫美学。必须使用鲜艳且不协调的色彩组合，如亮粉、青绿、鲜黄和橙色，创造故意的视觉冲突。几何形状是核心元素，应大量使用不规则图形、锯齿形、波浪线和彩色网格，这些形状应随意排列且看似不遵循传统设计规则。纹理对比很重要，应混合使用点状图案、条纹和几何网格。排版应活泼且不拘一格，使用几何感强的无衬线字体，可添加阴影或3D效果增强视觉冲击力。装饰元素应包含孟菲斯标志性的彩色条纹、圆点、Z字形和任意形状的色块。必须打破传统的网格系统，元素可自由浮动且看似随意放置。可添加1980年代流行文化元素如霓虹灯、电视机或卡带的抽象表现。整体设计应呈现出夸张、活泼且反传统的视觉冲击，仿佛来自80年代的未来主义想象，参考埃托雷·索特萨斯(Ettore Sottsass)的作品和MTV早期的视觉风格，体现"反功能主义"的设计理念。

29. 德国表现主义风格 (German Expressionism)
采用德国表现主义风格设计，体现20世纪初期德国表现主义运动的强烈情感表达。背景应使用深色调如深蓝、黑色或暗红色，创造戏剧性氛围。必须使用强烈的明暗对比和扭曲变形的形态，线条应锐利且富有动感，呈现出内在情绪的外化。排版应不规则且具表现力，文字可呈现倾斜或不稳定感，标题应使用粗犷、锐利的哥特式字体或手写风格字体。色彩应强烈且具象征性，偏好使用黑色、深红、黄色和深绿等高对比度组合。图像处理应添加木刻版画效果，强调粗犷的线条和夸张的明暗对比。阴影是关键元素，应使用长而尖锐的投影创造紧张感和不安氛围。可添加象征性元素如尖塔、扭曲的人物剪影或锯齿状山脉。整体设计应呈现出强烈的情感张力和心理深度，仿佛一页来自德国表现主义电影《卡里加里博士的小屋》的场景设计或卡尔·施密特-罗特卢夫(Karl Schmidt-Rottluff)的木刻版画，体现"情感真实大于形式真实"的艺术理念。

## 基本要求
**每种风格都应包含以下元素，但视觉表现各不相同：**
* 标题和副标题：根据风格调整字体、大小、排版方式
- 主标题字号需要比副标题和介绍大三倍以上，适合手机观看的字体
* 引用区块：设计独特的引用样式，体现风格特点
* 核心要点列表：以符合风格的方式呈现列表内容
* 二维码区域：将二维码融入整体设计
* **画布与布局约束**: 整个设计必须构成一个**完全独立的视觉单元**，并被严格限制在**一个无边框 (`border: 0;`) 的主容器 `div`（画布）内**。此画布的尺寸必须是 **宽度 440px，最大高度 1280px**。**所有**视觉元素（包括文字、图像、装饰、背景、阴影、效果）**绝对不能超出**此画布`div`的边界。推荐在画布 `div` 上明确设置 `overflow: hidden;` 来强制约束内容。此画布 `div` 将是**最终用于外部截图或程序化下载的完整区域**。
* **字体缩放**：根据整体布局调整字体大小，确保在有限空间内的可读性，避免文字过大导致溢出。

**技术实现**
- 使用HTML5、Font Awesome、Tailwind CSS和必要的JavaScript
  * Font Awesome: [https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css](https://lf6-cdn-tos.bytecdntp.com/cdn/expire-100-M/font-awesome/6.0.0/css/all.min.css)
  * Tailwind CSS: [https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css](https://lf3-cdn-tos.bytecdntp.com/cdn/expire-1-M/tailwindcss/2.2.19/tailwind.min.css)
  * 中文字体: [https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap](https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;700&display=swap)
- 使用现代CSS技术（如flex/grid布局、变量、渐变），**并利用这些技术确保内容不会超出指定的容器边界**。
- 可考虑添加微妙的动效，如页面载入时的淡入效果或微妙的悬停反馈
- 使用CSS变量管理颜色和间距，便于风格统一**和边界控制**。
- 对于液态数字形态主义风格，必须添加流体动态效果和渐变过渡
- 对于超感官极简主义风格，必须精确控制每个像素和微妙的交互反馈
- 对于新表现主义数据可视化风格，必须将数据以视觉化方式融入设计**，同时确保可视化元素不超出边界**。

**输出要求**
- **输出为独立画布**: 最终生成的 HTML/CSS 代码应**仅包含海报本身的视觉呈现**，完全封装在上述 **440px x (≤1280px) 的无边框画布 `div`** 中。确保**任何视觉元素（包括边框、阴影、外发光等效果）都计算在此尺寸内，绝不溢出画布边界**。
- 代码应当优雅且符合最佳实践，CSS应体现出对细节的极致追求
- 设计一个边框为0的div作为画布，确保生成图片无边界
- 最外面的卡片需要为直角
- 将我提供的文案提炼为50-60字以内的中文精华内容
- 对主题内容进行抽象提炼，只显示列点或最核心句引用，让人阅读有收获感
- 考虑海报的信息布局，文字必须占据页面至少70%的空间，**但需在不溢出的前提下进行排布**。
- 二维码截图地址：（必须显示）：{二维码图片链接}
- 二维码尺寸：80px 宽度，80px 高度
- 二维码旁边显示文案：微信扫码查看
- 空间名称：（必须显示）：{空间名称}   
- 空间头像：（必须显示）：{头像链接}
- 产品logo：（必须显示）：{logo 链接}
请以国际顶尖杂志艺术总监的眼光和审美标准，创造风格迥异但同样令人惊艳的数字杂志式卡片，让用户感受到"这不是普通的信息卡片，而是一件可收藏的数字艺术品"。

## 评估标准
**产出评估将基于以下维度：**

1.  **✨ 风格执行与契合度:**
    *   **艺术装饰精髓:** 是否精准且富有创意地体现了所选风格的要求？
    *   **视觉统一性:** 整体设计元素（字体、颜色、线条、装饰）是否高度统一，符合选定风格？

2.  **📐 版面布局与视觉设计 (自由比例宽度440px，高度不超过1280px):**
    *   **画布完整性与尺寸约束**: 整个视觉设计是否完全包含在指定的 440px 宽度、最大 1280px 高度的无边框画布 div 内？是否存在任何形式的视觉溢出（内容、背景、效果等）？画布 div 本身是否无可见边框？
    *   **空间利用与平衡:** 在有限空间内，布局是否平衡、舒适？留白是否恰当？
    *   **信息层级:** 主标题、副标题、引言、要点、页脚信息的视觉层级是否清晰、主次分明？
    *   **字体排印:** 字体选择（如 Noto Serif SC, Poppins）和排版是否符合风格，同时保证在目标尺寸下的易读性？
    *   **元素精致度:** 分隔线、列表图标等装饰性元素是否精致且与风格协调？

3.  **📝 内容呈现与提炼:**
    *   **核心信息传达:** 是否清晰有效地传达了待处理内容的核心要点？
    *   **文案精炼度:** 引用和要点是否符合提示词中简洁、有收获感的要求？

4.  **💻 技术实现与代码质量:**
    *   **HTML 结构:** HTML 是否语义化、结构清晰？
    *   **CSS 应用:** Tailwind CSS 和内联样式的使用是否高效、规范？CSS 是否易于理解？
    *   **资源加载:** 外部资源（字体、图标库）是否正确加载？（性能在此处相对次要，但基础加载需保证）
    *   **规范性:** 是否包含所有必需元素（二维码、头像、空间名称、产品 logo等）且显示正确？

5.  **🌟 整体效果与完成度:**
    *   **专业性与美观度:** 海报整体是否呈现出专业、高级、符合"数字艺术品"定位的视觉效果？
    *   **细节处理:** 是否有明显的瑕疵或未完成部分？


## 待处理内容
- 封面文案：{文档内容}

只返回html代码，不返回文字说明
```


## UI

[https://www.figma.com/design/vmtLg2SSL4M3D2SDPij0OE/UI_web?node-id=3114-351&t=0hqKuEJbM9CRC3fG-1](https://www.figma.com/design/vmtLg2SSL4M3D2SDPij0OE/UI_web?node-id=3114-351&t=0hqKuEJbM9CRC3fG-1)



