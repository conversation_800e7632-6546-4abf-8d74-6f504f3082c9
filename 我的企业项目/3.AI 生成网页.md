项目三：AI 生成网页
# **文档目的**

本文档旨在详细描述将用户在 Flowus 中创建的**笔记页面**或通过 **AI 搜索获得的回答内容**，一键转化为一个具备现代化、精致视觉风格的独立网页，**并提供便捷的发布与分享能力**。该功能旨在提升用户内容的分享价值和可读性，提供比简单文本复制更高级的展示方式，并拓展 AI 回答的展示和传播渠道。

# **文档范围**

本文档说明了用户在**笔记页面**或 **AI 搜索结果**触发转换操作后，系统如何处理内容，调用LLM 生成结构化  HTML  代码片段，并结合预设的  HTML  框架和 CSS 样式，最终生成一个包含完整网页代码的输出供用户使用。

# **问题背景**

用户在 Flowus 中创作或通过 AI 搜索获得的内容，难以便捷地以美观、结构化的形式分享到外部。将内容手动转换为网页或依赖第三方发布工具既耗时又需要专业知识。**特别是 AI 搜索结果，通常仅为纯文本，缺乏视觉吸引力和易于分享的格式。目前即使生成了 HTML 代码，用户仍需自行解决托管和发布问题。**

# **用户故事**

- **作为一名内容创作者**，我希望将我在 Flowus 里写好的文章/教程，一键转换成一个排版精美、风格统一的网页，**并直接发布获得一个公开链接**，方便我分享到我的微信或社交媒体。

- **作为一名 AI 搜索用户**，我希望将 AI 搜索返回的有价值的回答内容，快速转换成一个结构清晰、视觉美观的网页，**并直接发布**，方便我分享给需要的人。**发布后，当我再次看到这个 AI 搜索结果时，能一眼看到我之前发布的网页链接。**

# **目标**

## 1. **业务目标**

- 提升用户内容的分享意愿和范围。

- 增强 flowus 作为内容创作平台的吸引力。

- 为用户提供一种便捷的内容发布/展示手段，增加产品价值。

- 为 AI 搜索结果提供更丰富的展示和分享方式，提升 AI 功能的使用体验和价值感知。

## 2. **产品目标**

- 提供一个简单、直观的“转换”入口（适用于页面和 AI 搜索结果）。

- 生成的网页在视觉上与预设的精致风格高度一致（统一的样式框架）。

- 页面内容能被准确识别并转化为对应的 HTML 结构（无论内容来源是页面还是 AI 回答）。

- 用户可以方便地获取已发布网页的公开 URL。

# **技术方案**

采用模块化生成方法，分离网页的**视觉风格和基础结构**与**实际文章内容**。实现这一目标，定义了以下**两个主要组件：**

### **1. HTML 风格框架**

**使用 gemini/claude 3.7 提前生成**

- **作用：** 定义并承载网页的整体视觉风格、基础结构和非内容区域元素。

- **内容：** 包含完整的 HTML 文档结构（如 <!DOCTYPE>, <html>, <head>, <body>）、所有的 CSS 样式规则（包括布局、排版、颜色、动画、数据视图样式等）、必要的全局 JavaScript 脚本以及固定的页脚等。

- **特性：** 这是一个预设且相对固定的模板，作为容器来包裹由大模型根据文章内容生成的可变内容区域。其主要职责是确保最终网页具备统一、精致的视觉呈现和良好的用户体验。

[HTML 风格框架](https://flowus.cn/ca089956-59ab-4ea3-be04-c1020b6466e0)



### 2. **内容区域生成提示词**

**使用长上下文窗口模型  qwen2.5-14b-instruct-1m 实时解析**

- **作用：** 指导 LLM 如何解析输入的文章内容，并将其转化为符合 HTML 框架样式约定的 HTML 代码片段。

- **内容：** 包含详细的指令，规定内容如何分解为章节、如何使用标题层级（h1, h2, h3, h4）、何时使用段落（p）、列表（ul/ol, li）、表格（table.comparison-table）、时间轴（div.timeline）、图表（div.chart-container 带 JSON 数据）等 HTML 元素，以及如何应用特定的 CSS 类名（如 .card, .conclusion, .highlight, .tag）和属性（如 style="animation-delay: X.Xs;"）以融入框架风格。

- **特性：** 这是一个结构化的文本指令，作为 LLM 的输入之一，引导其理解原始文章的逻辑结构，并输出仅包含文章核心内容部分的 HTML 代码（例如，从 .article-header 开始，到最后一个 <section> 结束），不含完整的 HTML 文档结构标签。

[内容区域生成提示词](https://flowus.cn/25b771e8-9a72-4622-835a-5af855285feb)
```HTML
你是一个专业的网页内容排版助手。你将收到一篇包含文章内容的文本，这些内容通常使用 Markdown 格式（如 `#`, `##`, `###` 标题，列表标记 (`*`, `-`, `1.`)，表格语法，图片链接 (`![alt text](image_url)`) 等）。你的任务是深入分析其结构，并生成符合特定 HTML 格式的**代码片段**。这些代码片段将被直接插入到一个已包含完整 HTML 框架（包括所有 CSS 样式，已引入 Font Awesome 图标库和 AOS 动画库）的页面中。你的输出**必须是纯 HTML 代码片段**，**不应包含** `<!DOCTYPE>`, `<html>`, `<head>`, `<body>` 等任何完整的 HTML 文档结构标签，**但应包含页脚 <footer> 标签作为输出的最后一个元素**。

生成的 HTML 代码片段应该位于框架内的 `<div class="container">` 内部，并遵循以下结构和规则：

1.  **文章头部 (`article-header`) 生成 (优先处理):**
    *   作为输出的第一个元素，生成一个 `<div class="article-header" data-aos="fade-up" style="animation-delay: 0.1s;">` 元素。
    *   **识别主标题和副标题：** 优先从文章开头或最顶层的 Markdown `#` 标题中识别文章的主标题。将其放入 `<div class="article-header">` 内的 `<h1>` 标签中。如果文章有明显的副标题或摘要（可能在主标题下方，或以特定格式标识），请将其放入紧随 `<h1>` 之后的 `<p class="subtitle">` 标签中。
    *   **提取元信息 (`article-meta`)：** 尝试从文章内容（特别是开头部分或末尾）中识别并提取文章类别/标签、阅读时间。将这两个元信息格式化为 `<div class="article-meta">` 块，使用 `<span>` 和 `<span class="divider"></span>` 分隔，如示例所示。如果无法提取，请根据情况留空或尝试合理估计（如阅读时间）。
    *   请根据提取到的信息填充 `<span>` 内容，并包含必要的 `<span class="divider"></span>`。**请记住提取到的文章主标题文本，它将在页脚中使用。**

2.  **主要章节 (`section`) 划分与标题 (基于 Markdown 优先，强制图标):**
    *   紧跟在 `<div class="article-header">` 之后，根据文章内容划分主要章节。
    *   **主要章节边界：** **严格**将 Markdown `#` 或 `##` 开头的行视为新的主要章节的开始标志。当遇到这些标题时，开始一个新的 `<section class="card" data-aos="fade-up">` 来包裹该标题及其后续内容。
    *   **尽可能多地使用 `section.card`：** 鼓励根据 Markdown `#` 或 `##` 标题的出现，将内容分解为多个 `<section class="card">`，以充分利用卡片样式进行模块化展示。
    *   **结论章节：** 如果文章内容最后包含总结、结论或主要 takeaway 部分，即使没有特定的 Markdown 标题标记，也请将其识别为一个独立的逻辑块，并包裹在 `<section class="conclusion" data-aos="fade-up">` 标签内。
    *   **章节标题 (`<h2>`)：** 每个 `<section class="card">` 或 `<section class="conclusion">` 的主要标题，无论在原文中对应 `#` 还是 `##`，都请使用 `<h2>` 标签。请不要在内容区域（article-header 之外）使用 `<h1>` 标签。
    *   **强制添加图标 (`<i>`)：** 在每个 `<h2>` 标签内部，紧跟在开始标签后，**必须**添加一个 `<i>` 标签作为图标。这个 `i` 标签需要同时包含 `class="icon"` 和一个反映章节主题的 **Font Awesome 图标类名**。请**分析**章节内容，选择最合适且有代表性的 Font Awesome 图标类名。通常使用 **Solid 风格**，类名为 `fas fa-[icon-name]` 的形式（例如 `fas fa-info-circle`, `fas fa-chart-bar`, `fas fa-lightbulb`, `fas fa-file-alt` 等）。例如：`<h2><i class="icon fas fa-chart-bar"></i>章节标题文本</h2>` **这些图标的颜色会根据主题自动切换，无需额外样式。**

3.  **次级标题 (`h3`, `h4`) (基于 Markdown 优先，强制场景卡片图标):**
    *   **严格**将原文中对应 `###` 的标题转换为 `<h3>` 标签。
    *   如果文章内容包含一系列并列的、简短的场景、例子或项目，并且这部分内容逻辑上适合这种展示，请使用 `<div class="scenario-grid">` 包裹，内部每个项目使用 `<div class="scenario-card" data-aos="fade-up">`。
    *   `scenario-card` 的标题使用 `<h4>`。在每个 `<h4>` 标签内部，紧跟在开始标签后，**必须**添加一个 `<i>` 标签作为图标。这个 `i` 标签需要同时包含 `class="icon"` 和一个反映场景卡片主题的 **Font Awesome 图标类名**。请**分析**场景卡片内容，选择最合适且有代表性的 Font Awesome 图标类名。通常使用 **Solid 风格**，类名为 `fas fa-[icon-name]` 的形式（例如 `fas fa-folder`, `fas fa-file`, `fas fa-flask`, `fas fa-cogs` 等）。例如：`<h4><i class="icon fas fa-folder"></i>场景卡片标题文本</h4>` **这些图标的颜色会根据主题自动切换，无需额外样式。**

4.  **引用块 (`blockquote`) (基于分析，鼓励使用):**
    *   请**分析**文章内容，识别出那些表达了重要观点、引人深思的句子、引用的名言、或者在段落中需要突出强调的独立陈述。
    *   将这些识别出的内容包裹在 `<blockquote>` 标签内。如果引用内容包含多个句子，可以将其分解为 `<blockquote><p>...</p><p>...</p></blockquote>` 结构。
    *   **鼓励在每个主要章节内，如果内容有合适的点，就考虑使用 `<blockquote>` 样式来突出重要陈述或引语，以提升视觉层次和阅读体验。** 这种样式的使用完全基于内容的**分析**和判断。Markdown 的引用语法 (`>`) 可以作为参考，但不强制要求原文必须有此标记。

5.  **代码块 (`code`, `pre`) (基于 Markdown 严格转换):**
    *   **严格**将 Markdown 中的行内代码（使用单个反引号 ` `` ` 包裹的文本）转换为 `<code>` 标签包裹。例如：`这是一个 `行内代码` 示例` 应转换为 `这是一个 <code>行内代码</code> 示例`。
    *   **严格**将 Markdown 中的代码块（使用三个或更多反引号 ``` 包裹的多行文本，可选带语言标识 ` ```lang\n...\n``` `）转换为 `<pre><code>...</code></pre>` 结构包裹。请保留代码块内的原始换行和缩进。例如：
        ```markdown
        ```python
        print("Hello, world!")
        ```
        ```
        应转换为：
        ```html
        <pre><code>print("Hello, world!")
        </code></pre>
        ```
    *   请注意，**不需要**在 `<pre>` 或 `<code>` 标签上添加任何额外的类名或样式属性来指定语言，框架的 CSS 已经处理了基础样式。
6.  **表格 (`table.comparison-table`) (基于分析，鼓励使用):**
    *   **优先识别并严格转换** Markdown 表格语法 (`|---|`) 为 `<table class="comparison-table">` 结构，包含 `<thead>` (表头) 和 `<tbody>` (表格体)，使用 `<th>` 定义表头单元格，`<td>` 定义普通数据单元格。
    *   **更重要的是：** 请**分析**文章内容中那些以清晰的结构列出数据、属性、比较点或分步信息的部分，即使它们没有使用标准的 Markdown 表格语法。如果这些内容逻辑上适合用表格展示（例如，有多个项目，每个项目有几个对应的属性或值），**鼓励**将其转换为 `<table class="comparison-table">` 结构。
    *   在进行分析转换时，请合理判断哪些行应该作为表头 (`<thead>` 中的 `<th>`)，哪些是表格体 (`<tbody>` 中的 `<td>`)。

7.  **文本内容排版 (基于 Markdown 优先):**
    *   **段落：** Markdown 中的普通文本段落（由空白行分隔）请转换为 `<p>` 标签包裹。
    *   **列表：** **严格**将 Markdown 中的列表 (`*` 或 `-` 开头的无序列表，`1.` 开头的有序列表) 转换为 `<ul>` (无序列表) 或 `<ol>` (有序列表)，列表项使用 `<li>`。
    *   **表格：** **严格**将 Markdown 表格转换为 `<table class="comparison-table">` structure，包含 `<thead>` (表头) 和 `<tbody>` (表格体)，使用 `<th>` 定义表头单元格，`<td>` 定义普通数据单元格。
    *   **图片：** **严格**识别文章内容中的图片链接（例如 Markdown 格式 `![alt text](image_url)` 或纯 URL）。将识别到的图片链接转换为 `<img src="image_url" alt="alt text" class="article-image">` 标签。`alt` 文本优先使用原文提供的，否则尝试根据图片周围的文字内容**分析**生成。图片标签通常应放置在它们所关联的段落之后，或者在它们所属于的章节内部作为一个独立的块级元素。为所有生成的 `<img>` 标签添加 `class="article-image"`。**若有出现图片链接，一定要显示图片。**

8.  **强调和标记 (基于分析，确保视觉效果):**
    *   **高亮 (`.highlight`)：** **不完全依赖** Markdown 加粗。请**分析**文章内容，识别核心概念、关键术语、重要的引语、结论性短语或其他需要视觉强调的文本，并使用 `<span class="highlight">` 标签包裹。Markdown 加粗可以作为一个参考，但最终决策基于你的**分析**。为了提升视觉效果，**请确保在每个主要章节（每个 `.card` 和 `.conclusion` 内）中至少有两处使用了 `<span class="highlight">` 进行高亮。**
    *   **标签 (`.tag`)：** 如果文章内容中有明确的类别、关键主题词或区分不同概念的名称，并且这些概念适合用标签形式展示，请基于内容**分析**添加 `<span class="tag [tag-type]">` 标签（例如 `<span class="tag typeA-tag">Type A</span>` 或 `<span class="tag typeB-tag">Type B</span>`）。这完全是基于内容的**分析**和判断。

9.  **动画属性 (`data-aos`, `style="animation-delay"`)：**
    *   为 `<div class="article-header">`、每个 `<section class="card">`、每个 `<div class="scenario-card">`、`<section class="conclusion">` 和 `<footer>` 元素添加 `data-aos="fade-up"` 属性，启用 AOS 动画。
    *   为 `<div class="article-header">`、每个 `<section class="card">`、`<section class="conclusion">` 和 `<footer>` 元素添加 `style="animation-delay: X.Xs;"` 属性。第一个元素 (`article-header`) 使用 `0.1s`，第二个元素 (第一个 card) 使用 `0.25s`，以此类推，延迟时间根据这些**非 scenario-card** 元素在内容区域出现的顺序递增（建议步长 0.15s 或 0.25s）。**注意：scenario-card 内部元素不添加 style="animation-delay"**。

10.  **页脚 (`footer`) 生成：**
    *   作为输出的最后一个元素，生成 `<footer>` 标签，并添加 `data-aos="fade-up"` 属性。
    *   为其添加 `style="animation-delay: Y.Ys;"` 属性，其中 `Y.Y` 是在所有 `<div class="article-header">`、`<section class="card">` 和 `<section class="conclusion">` 元素的最后一个延迟基础上再增加一个步长（例如，如果最后一个 conclusion 的延迟是 1.15s，页脚的延迟可以是 1.3s 或 1.4s）。
    *   页脚的内部结构如下，请严格按照此结构填充内容：
        ```html
        <footer>
            <p>[在此显示步骤1中识别到的文章主标题]</p>
            <p>网页由 webbuilder 生成，仅供参考</p> (严格遵守)
            <a href="https://code.webbuilder.site" class="text-indigo-600 hover:text-indigo-800 transition-colors">webbuilder</a>(严格遵守)
        </footer>
        ```
    *   请确保将步骤1中识别到的文章**主标题文本**填写到第一个 `<p>` 标签内。

11.  **输出格式要求：**
    *   你的输出**只能是 HTML 代码片段**。它必须以 `<div class="article-header">` 标签开始，并在包含所有章节 (`<section>`) 后，以 `<footer>` 标签结束。**严禁**输出任何 `<!DOCTYPE>`, `<html>`, `<head>`, `<body>` 等标签或框架的其他部分。

请根据以上规则，将下面的文章内容转换为符合要求的 HTML 代码片段：

**文章内容：**
[]
```







### 3. 替换<title></title>

- HTML 框架中的`<title></title>` 需要根据 LLM 生成的内容动态替换。

![image.png](https://tc-cdn.flowus.cn/oss/42d01792-d95a-4fcd-bc85-3b533ab36f14/image.png?time=1753253100&token=cb90074096db98fe954a7708d145fc09ac895c95fa18d50252750431f94869bd&role=free)

- 生成的 footer 格式如下，取`[在此显示步骤1中识别到的文章主标题]`填充到`<title></title>`中

```HTML
  <footer>
      <p>[在此显示步骤1中识别到的文章主标题]</p>
      <p>Powered by webbuilder</p>
      <a href="https://code.webbuilder.site" class="text-indigo-600 hover:text-indigo-800 transition-colors">webbuilder</a>
  </footer>
```




### **4. 方案优势**

这种设计方法的核心优势在于：**实现风格/结构的定义与内容生成的解耦**。

在实践中发现，v3 的网页生成能力在美观度和稳定性上现阶段都不如 claude 3.7 和 Gemini，如果将完整的 HTML 生成任务让 v3 独立实时生成，会由于**上下文窗口限制频繁出现代码生成中断**的问题，或者由于 v3 本身能力有限导致生成后网页**美观度不足**的问题。



**因此采用分离生成的方式可以有以下优势：**

- **提升生成稳定性与准确性：** 将复杂的网页整体结构和样式硬编码在固定的框架中，大大降低了国产模型从零生成完整网页代码的难度和出错概率。只需专注于理解文章内容并将其结构化为 HTML 片段。

- **优化生成速度：** 国产模型只需生成文章核心内容的 HTML 片段，而非完整的 HTML、CSS 和 JS 代码，从而减少了生成 token 数量和计算复杂度，提高了实时生成的速度。

- **灵活利用LLM 模型的特性：**Claude 3.7 和 Gemini 适合处理代码，保证视觉风格。qwen2.5-14b-instruct-1m 适合处理长文档。它支持最长可达 100 万个 token 的超长上下文。

- **确保最终美观度和一致性：** 网页的视觉风格由专业设计并预设在 HTML 框架中（**由更强大的模型生成，如 Claude 3.7 和 Gemini**） ，保证了所有生成的网页都能拥有一致且高质量的美学表现，避免了 v3 在自由生成样式时可能出现的风格偏差和视觉问题。



**总结：**通过将**风格定义**和**内容结构化**任务分离的方式，可以更**有效、灵活利用不同 LLM 特性**，提供稳定、快速且美观的页面转网页功能。将复杂的风格定义部分让更适合 coding 的 LLM 模型（Claude，Gemini）提前生成。而内容部分的代码片段，可以实时地让 qwen2.5-14b-instruct-1m  生成。

# **功能范围**

#### 1. **转换入口:**

- **笔记页面入口：**在公开分享中增加**“生成网页”**。

![image.png](https://tc-cdn.flowus.cn/oss/68673b54-0c7c-41e1-95bd-a41925293d94/image.png?time=1753253100&token=70405bf111196d6ddbedea9799d533508a523cdea2bec67a94f42cf7330bf14e&role=free)

- **AI 搜索页面入口：**在 AI 搜索结果**回答底部**增加**“转成精美网页”**（侧边 AI 搜索窗口也需要）

![image.png](https://tc-cdn.flowus.cn/oss/d11c23a0-423b-4299-830d-0909a4f499b1/image.png?time=1753253100&token=ea8ac16145f5cafe6ef30fc1a6239dc176f0ead3b44e95e68a98818891179b49&role=free)

#### 2. **内容处理:** 

- 读取并提取选定内容。将提取的内容以及本次 PRD 中定义的**“内容区域生成提示词”**发送给 LLM，LLM 根据提示词解析内容，并生成符合预设结构的 HTML 代码片段。

#### 3. **框架集成:** 

- 将 LLM 生成的 HTML 内容片段嵌入到预设的 HTML 风格框架中，生成完整的网页代码。

#### 4. **网页发布:**

- 系统应将生成的完整 HTML 代码保存到后端存储。

- 系统应为每个发布的网页生成一个唯一的、可公开访问的 URL。

#### 5. 分享网页:

- 在最终生成的网页中添加分享按钮，支持复制链接分享和扫码分享；

# 核心交互流程

## 1. 笔记页面生成网页交互流程

当用户点击“**生成网页**”按钮后，将触发以下交互流程：

#### 1. 内容有效性检查

前端进行初步的内容检查，例如内容是否为空。如果为空，则弹窗提示用户输入内容，而不是直接调用后端服务。

![image.png](https://tc-cdn.flowus.cn/oss/961195ae-0451-4931-b968-9c96387add48/image.png?time=1753253100&token=e4b155b7c88aa3c6a67a45023405370991e10e78536b7e46c5c057b8fdf332f2&role=free)

#### 2. 风格选择

显示风格选择弹窗，默认选择“毛玻璃风格”；

![image.png](https://tc-cdn.flowus.cn/oss/687ee084-0fc5-47e4-b456-aee5ffe4c06b/image.png?time=1753253100&token=e433df87c6276a30a118f338fd65260aaf9f8933a71280e6b9f68bd88393eb9e&role=free)

#### 3. 新标签页打开网页

- 选择风格后点击**“确认生成”**按钮后，前端会先向后端请求一个用于发布新内容的唯一、永久的URL；

- 前端在新标签页打开这个由后端分配的URL，并在该页面内展示加载状态；

- 当内容生成完毕后，后端会将生成好的HTML内容与这个URL关联起来，用户访问该URL时就能看到最终的网页；

#### 3. 加载过程

![image.png](https://tc-cdn.flowus.cn/oss/99835e78-4a37-4195-8c9e-c083c59dba17/image.png?time=1753253100&token=234b59c26afa31f88cda1d8131f67fae75d2f6890b92f678e54a7a932501e33d&role=free)

- **标题：**🚀 正在为您生成精美网页...

- **iframe：**在 iframe 中模拟 AI 逐字逐句地输出 HTML 代码的过程；

- **动画：**在 iframe 左侧显示正在扫码笔记内容/回答内容的动效；（@大象）

- **下方配上动态的提示文本：**

  - 正在初始化生成引擎...

  - 正在处理文本内容...

  - 内容结构已分析完毕，正在整合...

  - 正在优化排版...

  - 即将完成！



#### 4. 新标签页的生成结果反馈

- **成功：**清除加载指示，并在当前页面内部直接渲染出生成好的网页内容。

- **失败：**显示错误信息

  - **标题：**🙁 抱歉，网页生成失败；

  - **内容：**内容解析超时，请关闭此标签页并返回原页面重试；

  - **按钮：**返回页面（点击后跳转回原笔记页面或 AI 搜索页面）*（可选）*

  ![image.png](https://tc-cdn.flowus.cn/oss/614b25d5-3da9-437e-ae66-deadc9d59af2/image.png?time=1753253100&token=c5e5ab79c8013ab9db92a3a8f5e155d07f7a6ff98c35dd2cd70a65f19b3e6ee7&role=free)

#### 5. 笔记页面前端按钮状态

笔记页面加载完成时，根据检测到的状态相应地更新按钮。

- **默认状态：**从未触发后端生成任务，按钮文本为**“生成网页”**，点击后显示**风格选择弹窗**；

- **处理中：**在后端任务进行中，生成按钮不可点击，显示网页预览图空状态，显示“**网页生成中...**”状态提示词；

![image.png](https://tc-cdn.flowus.cn/oss/ed7bbaeb-eaa7-45c4-9d7b-33cfc917929f/image.png?time=1753253100&token=a14392da1a5932c5aaf34767b1e5b301e2c5fbef95b94666d6b2af7bb829e119&role=free)

- **成功：**

  - 当后端任务成功结束后，生成按钮不可点击，显示网页预览图，显示“**浏览网页**”按钮，点击按钮打开最终生成的页面链接；

  - 显示**“重新生成”**按钮，点击后显示**风格选择弹窗**，确认后发起一次全新的生成流程，更新已有的网页（旧的 URL）。

![image.png](https://tc-cdn.flowus.cn/oss/4f2c3601-2b4e-41cf-aa45-0c859c6fda7d/image.png?time=1753253100&token=c261a646602674e352acfff9dce23fbb5e3b58aa0feb3c6b14df3f550ea1def4&role=free)

- **失败后恢复为默认状态：**若生成失败后重新加载笔记页面，检查并恢复按钮状态。按钮恢复为最初始的**“生成网页”**。

#### 6. 后端任务

- 后端任务不可取消，若用户关闭标签页，后端任务仍进行；

### 7. 笔记页面其它交互与逻辑细节

#### 更新笔记

- 如果用户对已有的笔记内容进行了修改，不会自动更新网页内容，除非用户点击“**重新生成**”按钮。

#### 删除笔记

- 删除了原始的笔记页面，生成的网页链接不可访问；

- 不可访问页面 UI 复用现有的公开分享笔记页面不可访问的 UI。

#### 关闭分享

- 关闭公开分享，生成的网页链接不可访问；

- 不可访问页面 UI 复用现有的公开分享笔记页面不可访问的 UI。

#### 设置付费订阅/设置密码

- 若页面设置**了付费订阅**或**密码**，**“生成网页”**按钮不可点击。hover 时提示用户关闭付费订阅或关闭密码设置才可以启用生成网页功能；

- 若已经成功生成了网页，用户点击**设置付费订阅/设置密码**，弹窗提示用户生成的网页链接将不可访问，用户确认之后，**“生成网页”**按钮不可点击，再次 hover 时提示用户关闭付费订阅或关闭密码设置才可以启用网页功能。

## 2. AI 搜索页面生成网页交互流程

当用户点击“**转成精美网页**”按钮后，将触发以下交互流程：

内容有效性检查、显示风格选择弹窗、新标签页打开网页、加载过程、生成结果反馈、后端任务同上。接下来将描述该功能在当前场景与**笔记页面场景**中有**差异**的地方。

#### 内容有效性检查

- **显示文案：**无有效回答内容，请重新生成回答。

#### 前端按钮状态

- **成功：**当后端任务成功结束后，隐藏按钮。在回答下方显示网页预览图，点击会打开最终生成的页面链接；

![image.png](https://tc-cdn.flowus.cn/oss/6cb9bf82-a328-4c9b-aae1-955356c78093/image.png?time=1753253100&token=fcaa8e6fe1c51d0561281382856f4aeb8d7110c9d6c26669ad662f7e00a05f8b&role=free)



#### 删除 AI 搜索记录

- 生成的网页链接不可访问；

- 不可访问页面 UI 复用现有的公开分享笔记页面不可访问的 UI。

## 3. 分享网页

- 在最终生成的网页中添加分享按钮，支持复制链接分享和扫码分享；

- 分享按钮参考现在的公开分享页面的“**分享**”按钮组件；

- 用户需登录才可以查看页面内容；

- 登录后显示“**返回空间**”按钮，跳转逻辑同现有组件。

![image.png](https://tc-cdn.flowus.cn/oss/6a96793a-f159-4226-b7d5-d989c2ad3eec/image.png?time=1753253100&token=9630cd49a7fac3b7b56539aa111f9d438a3ae9e5ed379d08dbcde2f8e7648b53&role=free)



## 设计稿

[https://www.figma.com/design/vmtLg2SSL4M3D2SDPij0OE/UI_web?node-id=3271-3713&t=BddCtFuJcNY8crHF-1](https://www.figma.com/design/vmtLg2SSL4M3D2SDPij0OE/UI_web?node-id=3271-3713&t=BddCtFuJcNY8crHF-1)

