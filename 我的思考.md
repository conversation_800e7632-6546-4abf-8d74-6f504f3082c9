作为 AI 产品经理，我平时的思考

从技术角度：

AI 这个行业是有一个 Tech Stack，最底层比如英伟达是做芯片的，做云的，做基座模型的比如 OpenAI，还有一些做 infram 的，最顶层是做应用的。我个人能发挥最大价值的地方在应用层。我主要关注如何把大模型这项技术翻译成一个实际的产品，一个会与实际的用户直接交互的产品，因为它是离用户最近的地方

**（去看 youtube 视频，有说为什么要关注在app 层）**

[www.youtube.com](https://www.youtube.com/watch?v=v9JBMnxuPX8)

![image.png](https://tc-cdn.flowus.cn/oss/9a8583fc-23a7-4dba-8514-b66257b20aa3/image.png?time=1753253100&token=91d9cbf37ae910933134cb0dac869e20dea1d6f23a27d59a4546e509f91d693e&role=free)



从职能角度

从大模型业务的链路角度考虑。研究人员发现大模型的新能力，然后开始进入工程，在工程之后产品化，产品化之后有 go to market，最后到用户手里。我发挥价值的地方在最后一公里，也就是产品到商业化到最终到用户手里这个过程，它可能是双向的，比如把产品推向市场，也可能是反过来的了，比如根据用户实际的需求去倒推做什么产品

![image.png](https://tc-cdn.flowus.cn/oss/38c813fd-63d6-419d-995b-cd25d71ad933/image.png?time=1753253100&token=1534b7d905e9ebf6a4db19b54fc954b6f21e69067dafd7acfed6bb10cf087d2c&role=free)



我的定位：我要做应用层的，连接产品和用户的事情

从产品层面，我如何将那些晦涩的技术以普通人能理解或者接受的方式普及到用户真正的生活里。从用户层面，我如何可以通过用户需求与洞察反哺给AI技术的应用开发与落地。



学什么

1. 模型的边界（能实现什么效果）

  1. 每天持续、深入使用模型

2. 发展趋势（未来 3-6 个月能实现什么效果）

  1. build for the future

3. 如何把技术变成好的产品，解决现实生活中的问题（需要发现和使用好的产品）

  1. 看海外的好产品，实际上手使用，去研究这些做产品的人怎么思考的

4. 如何把 AI 产品的价值传递给用户

  1. 产品的定位，如何找PMF，如何 go to market

  

跟谁学（信息源）

现在这个行业现状大部分的一手信息还是在英文世界

![image.png](https://tc-cdn.flowus.cn/oss/ce6c5925-498b-4598-9f3a-dde2557aee78/image.png?time=1753253100&token=d61e7da6b3fd3e8484e1e46144107280371ec2c84ecab698264642668cba6342&role=free)

去youtube 上把产品的 PM 或者创始人的演讲看一遍

