项目一：AI 搜索产品

# 项目背景

我们之前计划在 9 月份推出一款面向中国市场的独立 AI 搜索产品，旨在通过新产品打开市场局面。然而，由于市场重心调整和资源有限的原因，该项目未能如期启动。这 3 个月我们在海外市场（特别是俄罗斯）的进展很不错，证明了我们的笔记产品是有市场竞争力的。

鉴于此，我们决定调整策略：暂不开发独立的 AI 搜索网站，而是将 AI 搜索功能整合到现有的笔记产品中，形成“笔记 + AI 搜索 + 创作”的完整解决方案。这一转变基于以下考虑：

#### 1. **市场定位调整**

- **差异化的竞争点**：重点宣传“笔记 + AI 搜索 + 创作”的组合，避免与纯 AI 搜索产品直接竞争，**同时凸显与 Notion 等竞品的不同之处。**

- **从国内市场到海外市场的转变**：现在在俄罗斯市场取得了一定成绩，整合 AI 功能有望进一步提升用户体验，提高订阅。

#### 2. 时机分析

- **时机分析：**现在单独做 AI 搜索有点晚了，但将 AI 搜索功能整合到现有的笔记产品中，反而能形成很好的差异化优势，特别是在我们已经打开的海外市场。

- **AI 搜索的市场热度**：虽然 AI 搜索市场已经有许多竞品，但与笔记和创作功能结合的形式仍有创新空间。尤其是在海外市场，搜索与内容创作的整合需求较高，用户更容易为提升生产力的完整解决方案买单。

#### **3. 市场推广**

- **资源的最优配置**：在俄罗斯市场，我们已经获得了一定的品牌影响力，资源利用会更高效，不用分散精力去运营独立产品，通过大使、KOL 扩大影响面，降低运营成本。

# AI 规划

阶段一：AI 搜索 + 记笔记

阶段二：AI 阅读（支持上传文件）

阶段三：AI 写作（支持逐步生成写作大纲后再生成内容）

---

阶段四：AI 会议纪要（支持接入 zoom 等会议产品，接入语音转文字 AI，自动生成笔记保存到空间，同步发送笔记链接到telegram）

阶段五：扩大 AI 上下文边界，连接其它系统包括 Slack、Google Drive 等等。

# 竞品调研

[AI 竞品调研](https://flowus.cn/8cc5fc23-9513-4369-9033-8d33b744dd85)

# **产品概述**

本产品结合了**笔记、搜索、写作和阅读**功能，通过 AI 驱动的方式帮助用户更高效地进行知识管理和创作。用户可以在一个平台上完成从**信息搜寻到创作**的全过程，结合 AI 助力实现 **边看边问、边搜边记、边记边写** 等功能。**深化 BuildIn 作为AI+生产力工具的核心定位。**

**亮点：**

- 笔记 + 搜索 + 写作 + 阅读 

- 边看边问、边搜边记、边记边写

- 完全在 BuildIn 内完成文章创作与 AI 互动

**目标：**

- **外观**：简洁直观，功能入口清晰，提升用户体验。

- **功能**：实现 AI 与用户的紧密联动，确保 AI 能在合适的场景下提供帮助。

- **知识库**：帮助用户构建个人知识库，并支持边问边看、边搜边记的需求。

- **创作：**为创作者提供产品内的完整工作流体验。

# 需求概述

**以下为 MVP 版本主要需求：**

- 目录树添加 AI 入口；

- 检索源支持连网搜索；

- 检索源支持选择页面；

- 首页支持选择页面快捷输入总结 prompt；

- 首页支持快捷输入写作 prompt

- 搜索历史记录；

- 支持保存回答页面为笔记页面；

- 回答页面支持生成大纲、脑图；

- 支持分享、重新回答、复制回答页面；

- 支持显示参考来源；

- 页面入口支持默认选择当前页面作为检索源；

- 支持页面中划词搜索；

- 支持搜索结果再划词搜索；

- 上线后 web 端用户第一次进入/切换页面时自动打开页面右侧 AI 对话框。

# 开源 AI 连网搜索

- [https://github.com/ItzCrazyKns/Perplexica](https://github.com/ItzCrazyKns/Perplexica)

- [https://github.com/memfreeme/memfree](https://github.com/memfreeme/memfree)



# 相关需求

[【BI】AI 功能介绍页设计及权益显示优化](https://flowus.cn/a5aa0a72-a666-41ba-95cb-4456cc3f7e5b)



# 需求详情

## 1. 搜索**功能**

#### 1.1 功能概览和说明

|功能|支持类型|功能说明|
|-|-|-|
|支持连网搜索|Google，Exa AI，Vector P0|连网开关默认关闭，可打开；|
|切换信息源|所有可访问的信息源 P0
空间知识 P0
仅 AI 模型问答 P0
BI 帮助中心 P0|1️⃣**默认设置：**默认选择所有可访问的所有信息源；
2️⃣**支持混合搜索：**
开启连网搜索后，可同时在互联网和端内信息源中获取信息
-----------------------
页面 AI
**默认设置：**默认选择当前页面|
|AI 模型支持|GPT-4o mini P0|支持切换 AI 模型|
||GPT-4o||
||Claude 3.5 Haiku||
||Claude3.5 Sonnet||
||O1-Mini||
||O1-Preview||
|多种提问格式|文本 P0||
||图片|1️⃣支持粘贴图片提问；
2️⃣支持上传的图片格式：PNG，JPEG|
||文件|1️⃣支持上传的本地文件格式：Txt，PDF，Docx，PPTX，Markdown，PNG，JPEG；
2️⃣本地文件上传后保存为空间文件；
3️⃣若一次上传多个文件，则保存为空间文件夹；
4️⃣支持选择空间内的文件或文件夹提问；|
||网址|支持粘贴网址提问|
|多种回答形式|文本 P0||
||思维导图 P0||
||大纲 P0||
||图片||
||视频||
|支持追问|-|提供 3 条与问题和回答相关联的追问|

#### 1.2 自动搜索决策

三种 RAG 优先级：

- 基于空间内容回答（空间内容、订阅内容、帮助中心）

- 基于互联网知识回答；

- 基于 AI 模型知识直接回答；

三种搜索场景：

- 空间总入口唤出 AI 搜索；

- 页面入口唤出 AI 搜索；

- 页面划词唤出 AI 搜索；

|搜索场景|RAG 优先级|备注|
|-|-|-|
|总入口|1. 空间内容
2. 连网搜索
3. AI 模型|问题与**空间内容**高度相关时，优先使用空间内容回答，当未能找到直接回答的信息时，进行互联网搜索或 AI 模型直接回答|
|页面入口|1. 页面内容
2. 空间内容
3. 连网搜索
4. AI 模型|问题与**当前页面**内容高度相关时，优先使用当前页面内容回答，当未能找到直接回答的信息时，依次降级检索|
|页面划词|1. 页面内容
2. 空间内容
3. 连网搜索
4. AI 模型|同上|



#### 1.3 切换不同检索源时的显示文案

![image.png](https://tc-cdn.flowus.cn/oss/e6e85aef-e8b9-4ef7-b1ee-927d0c9b8c94/image.png?time=1753253100&token=737695e13ea55444ad2c2b2d1c12e70941a24dbe919ac2369be82ffb9d5bd5c6&role=free)

|检索源|选中时的文案显示||
|-|-|-|
|All sources I can access|All sources||
|{空间名称}|In my space||
|My Subscriptions|In my subscriptions||
|AI model only|AI model only||
|BuildIn Help Center|In BuildIn Help Center||
|Select pages|# selected|#——已选的页面数量|

## 2. 参考来源显示规则

- 优先显示相关度高的信息源；

- 若用户未开启连网搜索，基于空间内容搜索后无任何参考的信息源，则使用模型直接回答，显示文案：

  - Find in AI knowledge. Generated using knowledge from GPT-4. Double-check for accuracy

## 3. 回答结果页功能

#### 3.1 主入口 AI 回答结果页

|功能|支持类型|功能说明|
|-|-|-|
|记笔记|新建笔记页面；P0
复制粘贴到选择的页面底部|-|
|分享回答|图片分享 P0|-|
||链接分享|未登录状态下也可查看搜索内容；|
|重新回答|切换信息源重新回答；
直接重新回答；P0
关闭/打开连网搜索重新回答 P0|-|
|复制回答|-|仅复制文本部分|
|查看大纲||参考页面目录，按照 markdown 标题等级显示|
|查看脑图||支持查看大图；
支持记笔记；|
|划词记笔记|新建笔记页面；
复制粘贴到选择的页面底部|选择文字记笔记|
|划词搜索唤起小助手||支持划词再搜索|



#### 3.2 页面入口 AI 回答结果

|功能|支持类型|功能说明|
|-|-|-|
|生成脑图|-|侧栏空间有限，点击后在回答下方生成脑图|
|记笔记|新建笔记页面；
复制粘贴到现有页面底部|-|
|复制回答|-|仅复制文本部分|
|插入页面|-|插入当前页面底部|
|重新回答|||
|分享回答|||

**对话记录默认显示：**再次进入页面点击 AI 入口后默认显示上次离开时的界面，不清空历史记录。



## 4. 开通向量化流程

- 未开通向量化前仍可进行连网搜索、与大模型问答、搜索帮助中心；

- 未开通前，首页 AI及页面 AI 输入框上方在每次切换进入页面时显示开通横条，用户可关闭横条；

![image.png](https://tc-cdn.flowus.cn/oss/af358e79-8720-43e2-8e10-dd3bb9e527d8/image.png?time=1753253100&token=e13327f80929e4b40ad054744b440654a53140454a7aa6f94b759baad31c944c&role=free)

![image.png](https://tc-cdn.flowus.cn/oss/800d9a60-1ff8-4ad5-ac12-1257b0d02761/image.png?time=1753253100&token=680d4d8a1386e49a295fb156e8c82e1dc092e2026efd5f2e3f0b77c4b31ce959&role=free)

- 开通前下拉菜单隐藏选择页面多选框；

- 开通前下拉菜单隐藏**页面搜索输入框**；

- 申请后显示排队状态；

![image.png](https://tc-cdn.flowus.cn/oss/c9edc5d9-0c42-4d69-840a-b8fd8c0655e6/image.png?time=1753253100&token=ea607456a09be708e592a6e8cea2a03806868c4ba798a01a0f55b053bf333620&role=free)

![image.png](https://tc-cdn.flowus.cn/oss/fbc2332e-a564-4046-a8bc-c7a467bbd811/image.png?time=1753253100&token=81de1371f16ee543d1e136343806ac12e0475d6704e6c37eba137dc9312caac4&role=free)

![image.png](https://tc-cdn.flowus.cn/oss/16e7c26d-4d94-466d-bda4-bececa0a8219/image.png?time=1753253100&token=b87c80099d9f1fcda14deb7c6c8b84f94368121ebf0ef30ade825aa669be31c8&role=free)

- @快捷键引用页面时处理

![image.png](https://tc-cdn.flowus.cn/oss/eb49e055-fc0c-4554-ae77-5980ebc1d19c/image.png?time=1753253100&token=055568ae510f8eb43bba215a5fbdd240057f91bb2016315d946c95ab5d27ce9d&role=free)

- 页面 AI 页面

![image.png](https://tc-cdn.flowus.cn/oss/cd7a4321-2274-470a-8f6b-3ee4e70d9b74/image.png?time=1753253100&token=f18a4683f08619c965c24c839dc7d2955cbabd998b64fbb993ee27c632d144cb&role=free)

## 5. 历史记录处理规则

1. 历史记录中**单条记录**显示的内容包括以下：

  - 首页输入框输入的问题；

  - 回答页面追问框输入的问题；

  - 回答页面划词搜索后小助手输入的问题；

    - 小助手的问题仍然在右侧显示，

2. 按照提问时间依次显示问题及回答。

## 6. Query 次数用完后处理 

### 6.1 内容显示

3. **~~显示部分回答：利用用户好奇心促单~~**

  ~~仅显示一屏内容，采用渐变遮罩对屏幕底部内容进行隐藏；~~

  ![image.png](https://tc-cdn.flowus.cn/oss/3618d1aa-e559-4fa7-96fd-63f0e9a0939d/image.png?time=1753253100&token=91c2f4115b217bc1910ea409fbfa60341909ba69d6ae05583e4490cc9bee27a2&role=free)

4. **隐藏大纲和脑图；**

5. **参考来源不限制浏览；**



### 6.2 升级引导

6. **购买按钮：**屏幕底部显示购买按钮和提示文案；

- 提示文案：You’ve run out of your AI quota. Get the BuildIn AI package so that I can answer more questions for you.

- 按钮文案：Get more AI quota

![购买按钮和提示文案](https://tc-cdn.flowus.cn/oss/a5786270-e7ee-47fb-99ef-f2eee8ed6719/image.png?time=1753253100&token=de9bd6b7cb81d1123cd969ba543f0e9f4252827b2122f21a87618720f6e65cad&role=free)
购买按钮和提示文案

7. **按钮交互：**点击【Get more AI quota】后弹出收银台和 toast 提示

  toast 文案为：🙁 You’ve run out of your AI quota;

  停留时间 3 s；

![收银台](https://tc-cdn.flowus.cn/oss/0e9bbf3d-b5ed-4052-afe0-7ba238464944/image.png?time=1753253100&token=1b0ce2f2c56394a46f55afe23eb0408a6741463e8d490c860a3a68588e3ffbd4&role=free)
收银台

8. **修改收银台文案包括：**

  - 标题：Get more AI query quota；

  - 说明：AI query quota will stack with multiple purchases.

  - 按钮：Purchase package

  

### 6.3 操作限制

9. **隐藏操作按钮：**隐藏底部操作按钮，分享、重新回答、记笔记、复制；

10. **划词搜索限制：**划词搜索后直接弹出收银台和 toast 提示；

11. **追问限制：**继续追问问题直接弹出收银台 和 toast 提示；



### 6.4 历史回答页面追问问题处理

12. **划词搜索限制：**划词搜索后直接弹出收银台和 toast 提示

13. **追问限制：**继续追问问题直接弹出收银台 和 toast 提示



### 6.5 页面侧边栏 AI 提问处理

交互同上，可参考原型图



### 6.6 页面中划词使用 AI Edit 处理

显示提示语和购买按钮

![image.png](https://tc-cdn.flowus.cn/oss/446d0f0d-2571-4458-ba1a-1ae03bb1e59d/image.png?time=1753253100&token=94ca62a9eb9f18cf8d20a79e626c43ea895a32d15a5843f37b53c94a4b095e67&role=free)



## 7. 搜索和查看业务逻辑

#### 7.1 历史记录状态判断：

- 优先检查记录的解锁状态；

- 已解锁的内容直接显示；

- 未解锁的内容再检查当前剩余次数；



#### 7.2 判断状态的时机：

- 采用**点击查看历史记录时**判断的方案，不查看时不自动解锁；

- 只需在查看时判断当前解锁状态和剩余次数；

- 用户可以自主选择要查看（解锁）哪些历史记录，保持用户对次数使用的控制权；



#### 7.3 新搜索流程：

- 后端始终执行搜索；

- 根据剩余次数决定前端展示方式；

- 保存时记录解锁状态；

- **有次数：**消耗次数->显示完整结果->保存至历史记录标记为已解锁；

- **无次数：**显示部分结果和遮罩->保存至历史记录标记为未解锁；



#### 7.4 状态持久化

- 每条记录都带有解锁状态；

- 确保已解锁内容不会重新被锁；

![image.png](https://tc-cdn.flowus.cn/oss/38a06c60-3ded-4efe-9cd5-3dbd359b1287/image.png?time=1753253100&token=59130b1a6ff4be4a8189e4999a6ba472d80589c70df65a8deb177cb202a1781a&role=free)



## 8. AI 翻译

[AI 翻译汇总](https://flowus.cn/6493734a-bb04-4d4a-aa70-39a0a207b8ab)

# AI 权益

- 仍延续当前购买空间 AI 权益包的处理

- 免费试用次数： 10 次/天，移除每月 12 次的限制



## 我的订阅 AI 功能与空间绑定

当前「我的订阅」内容权限是账号层级，需要考虑 AI 功能权限的归属层级。

**订阅内容账号级别统一**：

- 「我的订阅」页面展示的内容（专栏列表等）是用户账号下的所有订阅内容，与空间无关。

- 无论用户从哪个空间入口进入「我的订阅」，展示的内容完全一致，确保一致性和易理解性。

**AI 功能与空间绑定**：

- 用户在「我的订阅」页面使用 AI 功能时，记录入口的空间 ID，并将 AI 次数的消耗与该空间绑定。

- 后端根据空间 ID 检查并扣减 AI 配额。

---

**用户体验一致**：

- AI 次数消耗逻辑与当前空间绑定，符合用户已有的认知模式。

**保持现有逻辑**：

- 保留了按空间消耗 AI 次数的设计，无需重新规划或创建新次数池。





# 原型图
[https://1py6vt.axshare.com/?id=4yok3h&g=1](https://1py6vt.axshare.com/?id=4yok3h&g=1)



# 设计稿

[www.figma.com](https://www.figma.com/design/USdhvTp88KcgdqjRnCzwkZ/%E8%AE%BE%E8%AE%A1%E9%9C%80%E6%B1%82?node-id=385-34&t=DE9CY6XModh6M27B-1)




# QA

[Buildin AI 测试](https://flowus.cn/f40d33bc-ffbc-4419-abf2-d9a28e44dcc2)

[ FlowUs AI 测试](https://flowus.cn/076402f2-6205-4a61-8f75-27328a946ad5)

